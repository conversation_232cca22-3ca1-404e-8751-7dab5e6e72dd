
// بيانات لعبة توصيل الكلمة بالصورة
const matchingData = [
    { 
        word: 'book', 
        arabic: 'كتاب', 
        image: 'images/book.png',
        category: 'school'
    },
    { 
        word: 'pen', 
        arabic: 'قلم', 
        image: 'images/pen.png',
        category: 'school'
    },
    { 
        word: 'pencil', 
        arabic: 'رصاص', 
        image: 'images/pencil.png',
        category: 'school'
    },
    { 
        word: 'bag', 
        arabic: 'حقيبة', 
        image: 'images/bag.png',
        category: 'school'
    },
    { 
        word: 'desk', 
        arabic: 'مكتب', 
        image: 'images/desk.png',
        category: 'school'
    },
    { 
        word: 'chair', 
        arabic: 'كرسي', 
        image: 'images/chair.png',
        category: 'school'
    },
    { 
        word: 'apple', 
        arabic: 'تفاحة', 
        image: 'images/apple.png',
        category: 'food'
    },
    { 
        word: 'banana', 
        arabic: 'موزة', 
        image: 'images/banana.png',
        category: 'food'
    },
    { 
        word: 'milk', 
        arabic: 'حليب', 
        image: 'images/milk.png',
        category: 'food'
    },
    { 
        word: 'water', 
        arabic: 'ماء', 
        image: 'images/water.png',
        category: 'food'
    },
    { 
        word: 'bread', 
        arabic: 'خبز', 
        image: 'images/bread.png',
        category: 'food'
    },
    { 
        word: 'cat', 
        arabic: 'قطة', 
        image: 'images/cat.png',
        category: 'animals'
    },
    { 
        word: 'dog', 
        arabic: 'كلب', 
        image: 'images/dog.png',
        category: 'animals'
    },
    { 
        word: 'fish', 
        arabic: 'سمكة', 
        image: 'images/fish.png',
        category: 'animals'
    },
    { 
        word: 'bird', 
        arabic: 'طائر', 
        image: 'images/bird.png',
        category: 'animals'
    },
    { 
        word: 'mummy', 
        arabic: 'ماما', 
        image: 'images/mother.png',
        category: 'family'
    },
    { 
        word: 'daddy', 
        arabic: 'بابا', 
        image: 'images/father.png',
        category: 'family'
    },
    { 
        word: 'baby', 
        arabic: 'رضيع', 
        image: 'images/baby.png',
        category: 'family'
    },
    { 
        word: 'boy', 
        arabic: 'ولد', 
        image: 'images/boy.png',
        category: 'family'
    },
    { 
        word: 'girl', 
        arabic: 'بنت', 
        image: 'images/girl.png',
        category: 'family'
    }
];

// وظيفة لتحميل لعبة توصيل الكلمة بالصورة
function loadMatchingGame() {
    let currentCategory = 'all';
    let currentItems = [];
    let selectedItems = [];
    let matchedPairs = 0;
    let totalPairs = 0;

    // اختيار فئة عشوائية أو استخدام "all"
    const categories = ['school', 'food', 'animals', 'family'];
    if (currentCategory === 'all') {
        currentCategory = categories[Math.floor(Math.random() * categories.length)];
    }

    // اختيار 5 عناصر عشوائية من الفئة المحددة
    const categoryItems = matchingData.filter(item => item.category === currentCategory || currentCategory === 'all');
    currentItems = shuffleArray(categoryItems).slice(0, 5);
    totalPairs = currentItems.length;

    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="matching-game">
            <h2>🔗 لعبة توصيل الكلمة بالصورة</h2>
            <p class="category-info">الفئة: ${getCategoryName(currentCategory)}</p>

            <div class="matching-container">
                <div class="matching-words">
                    <h3>الكلمات</h3>
                    <div class="words-list">
                        ${currentItems.map((item, index) => `
                            <div class="word-item" data-index="${index}" data-word="${item.word}">
                                ${item.arabic} (${item.word})
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="matching-images">
                    <h3>الصور</h3>
                    <div class="images-list">
                        ${shuffleArray(currentItems.map((item, index) => ({ ...item, index }))).map(item => `
                            <div class="image-item" data-index="${item.index}" data-word="${item.word}">
                                <img src="${item.image}" alt="${item.word}" class="matching-image"
                                     onerror="this.style.display='none'">
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="matching-controls">
                <button class="matching-btn" onclick="checkMatches()">التحقق من الإجابات</button>
                <button class="matching-btn" onclick="resetMatching()">إعادة المحاولة</button>
                <button class="matching-btn" onclick="nextGame()">التالي</button>
            </div>

            <div class="matching-progress">
                <span>الأزواج المتطابقة: ${matchedPairs} من ${totalPairs}</span>
            </div>
        </div>
    `;

    // إضافة أحداث النقر
    addMatchingEventListeners();
}

// الحصول على اسم الفئة باللغة العربية
function getCategoryName(category) {
    const names = {
        'school': 'الأشياء في الصف',
        'food': 'الطعام والشراب',
        'animals': 'الحيوانات',
        'family': 'العائلة',
        'all': 'كل الفئات'
    };
    return names[category] || category;
}

// إضافة أحداث النقر لعناصر اللعبة
function addMatchingEventListeners() {
    // اختيار الكلمات
    document.querySelectorAll('.word-item').forEach(item => {
        item.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            const word = this.dataset.word;

            if (this.classList.contains('selected')) {
                this.classList.remove('selected');
                selectedItems = selectedItems.filter(i => i.index !== index);
            } else {
                this.classList.add('selected');
                selectedItems.push({ index, word, type: 'word' });
            }
        });
    });

    // اختيار الصور
    document.querySelectorAll('.image-item').forEach(item => {
        item.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            const word = this.dataset.word;

            if (this.classList.contains('selected')) {
                this.classList.remove('selected');
                selectedItems = selectedItems.filter(i => i.index !== index);
            } else {
                this.classList.add('selected');
                selectedItems.push({ index, word, type: 'image' });
            }
        });
    });
}

// التحقق من الإجابات
function checkMatches() {
    if (selectedItems.length < 2) {
        enhancedSpeak('يرجى اختيار كلمة وصورة على الأقل', 'ar');
        return;
    }

    // التحقق من الأزواج المتطابقة
    let correctMatches = 0;
    let hasWord = false;
    let hasImage = false;

    // التحقق إذا كان هناك كلمة وصورة مختارتين
    selectedItems.forEach(item => {
        if (item.type === 'word') hasWord = true;
        if (item.type === 'image') hasImage = true;
    });

    if (!hasWord || !hasImage) {
        enhancedSpeak('يرجى اختيار كلمة وصورة', 'ar');
        return;
    }

    // التحقق من كل زوج
    const wordItems = selectedItems.filter(item => item.type === 'word');
    const imageItems = selectedItems.filter(item => item.type === 'image');

    wordItems.forEach(wordItem => {
        imageItems.forEach(imageItem => {
            if (wordItem.word === imageItem.word) {
                correctMatches++;
                // تلوين العناصر المتطابقة باللون الأخضر
                document.querySelector(`.word-item[data-index="${wordItem.index}"]`).classList.add('correct');
                document.querySelector(`.image-item[data-index="${imageItem.index}"]`).classList.add('correct');
            }
        });
    });

    // تسجيل الإجابة
    const isCorrect = correctMatches > 0;
    recordAnswer(isCorrect);

    // تحديث عدد الأزواج المتطابقة
    matchedPairs += correctMatches;
    document.querySelector('.matching-progress span').textContent = `الأزواج المتطابقة: ${matchedPairs} من ${totalPairs}`;

    // عرض رسالة النتيجة
    const gameContent = document.querySelector('.matching-game');
    const resultMessage = document.createElement('div');
    resultMessage.className = 'matching-result';

    if (isCorrect) {
        resultMessage.innerHTML = `
            <div class="result-success">
                <p>أحسنت! لقد وجدت ${correctMatches} زوج${correctMatches > 1 ? 'اً' : ''} متطابقاً</p>
                <p>Great! You found ${correctMatches} matching pair${correctMatches > 1 ? 's' : ''}</p>
            </div>
        `;
        enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
        celebrateCorrectAnswer();
    } else {
        resultMessage.innerHTML = `
            <div class="result-error">
                <p>حاول مرة أخرى! لا توجد أزواج متطابقة</p>
                <p>Try again! No matching pairs found</p>
            </div>
        `;
        enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
    }

    // إضافة النتيجة إلى اللعبة
    const existingResult = gameContent.querySelector('.matching-result');
    if (existingResult) {
        existingResult.remove();
    }
    gameContent.appendChild(resultMessage);

    // الانتقال للسؤال التالي بعد فترة
    setTimeout(() => {
        nextGame();
    }, 3000);
}

// إعادة المحاولة
function resetMatching() {
    // إعادة تعيين العناصر المختارة
    selectedItems = [];
    matchedPairs = 0;

    // إعادة تعيين واجهة المستخدم
    document.querySelectorAll('.word-item, .image-item').forEach(item => {
        item.classList.remove('selected', 'correct');
    });

    // تحديث التقدم
    document.querySelector('.matching-progress span').textContent = `الأزواج المتطابقة: ${matchedPairs} من ${totalPairs}`;

    // إزالة النتيجة السابقة
    const gameContent = document.querySelector('.matching-game');
    const existingResult = gameContent.querySelector('.matching-result');
    if (existingResult) {
        existingResult.remove();
    }
}

// إضافة أنماط CSS لعبة التوصيل
const matchingStyles = document.createElement('style');
matchingStyles.textContent = `
    .matching-game {
        text-align: center;
        padding: 20px;
    }

    .category-info {
        font-size: 1.2rem;
        color: #4CAF50;
        margin-bottom: 20px;
        font-weight: bold;
    }

    .matching-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .matching-words, .matching-images {
        flex: 1;
        min-width: 300px;
        margin: 0 10px;
    }

    .matching-words h3, .matching-images h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 1.3rem;
    }

    .words-list, .images-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .word-item, .image-item {
        padding: 15px;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s;
        border: 2px solid #e9ecef;
        text-align: center;
    }

    .word-item {
        background: #f8f9fa;
        font-size: 1.1rem;
        font-weight: bold;
    }

    .image-item {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .matching-image {
        width: 100px;
        height: 100px;
        object-fit: contain;
        margin-bottom: 5px;
    }

    .word-item:hover, .image-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .word-item.selected, .image-item.selected {
        border-color: #2196F3;
        background: rgba(33, 150, 243, 0.1);
    }

    .word-item.correct, .image-item.correct {
        border-color: #4CAF50;
        background: rgba(76, 175, 80, 0.1);
    }

    .matching-controls {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .matching-btn {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s;
    }

    .matching-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .matching-progress {
        margin-top: 20px;
        font-size: 1.1rem;
        font-weight: bold;
        color: #333;
    }

    .matching-result {
        margin-top: 20px;
        padding: 15px;
        border-radius: 10px;
    }

    .result-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .result-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
        .matching-container {
            flex-direction: column;
        }

        .matching-words, .matching-images {
            margin: 10px 0;
            width: 100%;
        }

        .matching-image {
            width: 80px;
            height: 80px;
        }
    }
`;

// إضافة الأنماط إلى الصفحة
document.head.appendChild(matchingStyles);
