// بيانات المستوى الأول - الصف الأول الابتدائي

// بيانات لعبة الحروف الأبجدية
const alphabetData = [
    { letter: 'A', word: 'Apple', arabic: 'تفاحة', image: 'images/apple.jpg', emoji: '🍎' },
    { letter: 'B', word: 'Ball', arabic: 'كرة', image: 'images/ball.png', emoji: '⚽' },
    { letter: 'C', word: 'Cat', arabic: 'قطة', image: 'images/cat.png', emoji: '🐱' },
    { letter: 'D', word: 'Dog', arabic: 'كلب', image: 'images/dog.png', emoji: '🐶' },
    { letter: 'E', word: 'Elephant', arabic: 'فيل', image: 'images/elephant.png', emoji: '🐘' },
    { letter: 'F', word: 'Fish', arabic: 'سمكة', image: 'images/fish.png', emoji: '🐟' },
    { letter: 'G', word: 'Giraffe', arabic: 'زرافة', image: 'images/giraffe.png', emoji: '🦒' },
    { letter: 'H', word: 'House', arabic: 'منزل', image: 'images/house.png', emoji: '🏠' }
];

// بيانات لعبة الألوان
const colorsData = [
    { color: 'Red', arabic: 'أحمر', hex: '#FF0000', items: ['Apple', 'Rose', 'Fire truck'] },
    { color: 'Blue', arabic: 'أزرق', hex: '#0000FF', items: ['Sky', 'Ocean', 'Blueberry'] },
    { color: 'Yellow', arabic: 'أصفر', hex: '#FFFF00', items: ['Sun', 'Banana', 'Lemon'] },
    { color: 'Green', arabic: 'أخضر', hex: '#00FF00', items: ['Grass', 'Tree', 'Frog'] },
    { color: 'Orange', arabic: 'برتقالي', hex: '#FFA500', items: ['Orange', 'Carrot', 'Pumpkin'] },
    { color: 'Purple', arabic: 'بنفسجي', hex: '#800080', items: ['Grapes', 'Flower', 'Eggplant'] },
    { color: 'Black', arabic: 'أسود', hex: '#000000', items: ['Night', 'Ant', 'Coal'] },
    { color: 'White', arabic: 'أبيض', hex: '#FFFFFF', items: ['Snow', 'Cloud', 'Milk'] }
];

// بيانات لعبة الأرقام
const numbersData = [
    { number: 1, word: 'One', arabic: 'واحد', items: 1 },
    { number: 2, word: 'Two', arabic: 'اثنان', items: 2 },
    { number: 3, word: 'Three', arabic: 'ثلاثة', items: 3 },
    { number: 4, word: 'Four', arabic: 'أربعة', items: 4 },
    { number: 5, word: 'Five', arabic: 'خمسة', items: 5 },
    { number: 6, word: 'Six', arabic: 'ستة', items: 6 },
    { number: 7, word: 'Seven', arabic: 'سبعة', items: 7 },
    { number: 8, word: 'Eight', arabic: 'ثمانية', items: 8 },
    { number: 9, word: 'Nine', arabic: 'تسعة', items: 9 },
    { number: 10, word: 'Ten', arabic: 'عشرة', items: 10 }
];

// بيانات لعبة العائلة
const familyData = [
    { word: 'Father', arabic: 'أب', image: 'images/father.png', description: 'Dad', emoji: '👨' },
    { word: 'Mother', arabic: 'أم', image: 'images/mother.png', description: 'Mom', emoji: '👩' },
    { word: 'Brother', arabic: 'أخ', image: 'images/brother.png', description: 'Boy sibling', emoji: '👦' },
    { word: 'Sister', arabic: 'أخت', image: 'images/sister.png', description: 'Girl sibling', emoji: '👧' },
    { word: 'Grandfather', arabic: 'جد', image: 'images/grandfather.png', description: 'Grandpa', emoji: '👴' },
    { word: 'Grandmother', arabic: 'جدة', image: 'images/grandmother.png', description: 'Grandma', emoji: '👵' }
];

// بيانات لعبة أجزاء الجسم
const bodyPartsData = [
    { word: 'Head', arabic: 'رأس', image: 'images/head.png', emoji: '🗣️' },
    { word: 'Eye', arabic: 'عين', image: 'images/eye.png', emoji: '👁️' },
    { word: 'Ear', arabic: 'أذن', image: 'images/ear.png', emoji: '👂' },
    { word: 'Nose', arabic: 'أنف', image: 'images/nose.png', emoji: '👃' },
    { word: 'Mouth', arabic: 'فم', image: 'images/mouth.png', emoji: '👄' },
    { word: 'Hand', arabic: 'يد', image: 'images/hand.png', emoji: '✋' },
    { word: 'Foot', arabic: 'قدم', image: 'images/foot.png', emoji: '🦶' }
];

// بيانات لعبة الحيوانات
const animalsData = [
    { word: 'Cat', arabic: 'قطة', image: 'images/cat.png', sound: 'Meow', emoji: '🐱' },
    { word: 'Dog', arabic: 'كلب', image: 'images/dog.png', sound: 'Woof', emoji: '🐶' },
    { word: 'Bird', arabic: 'طائر', image: 'images/bird.png', sound: 'Tweet', emoji: '🐦' },
    { word: 'Elephant', arabic: 'فيل', image: 'images/elephant.png', sound: 'Trumpet', emoji: '🐘' },
    { word: 'Monkey', arabic: 'قرد', image: 'images/monkey.png', sound: 'Ooh ooh', emoji: '🐵' },
    { word: 'Lion', arabic: 'أسد', image: 'images/lion.png', sound: 'Roar', emoji: '🦁' }
];

// بيانات لعبة الطعام
const foodData = [
    { word: 'Apple', arabic: 'تفاحة', image: 'images/apple.jpg', category: 'fruit', emoji: '🍎' },
    { word: 'Banana', arabic: 'موزة', image: 'images/banana.png', category: 'fruit', emoji: '🍌' },
    { word: 'Cake', arabic: 'كعكة', image: 'images/cake.png', category: 'dessert', emoji: '🍰' },
    { word: 'Water', arabic: 'ماء', image: 'images/water.png', category: 'drink', emoji: '💧' },
    { word: 'Milk', arabic: 'حليب', image: 'images/milk.jpg', category: 'drink', emoji: '🥛' },
    { word: 'Bread', arabic: 'خبز', image: 'images/placeholder.svg', category: 'food', emoji: '🍞' }
];

// وظائف ألعاب المستوى الأول

function loadAlphabetGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(alphabetData).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(alphabetData.filter(item => item.letter !== question.letter)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="alphabet-game">
                <h2>🔤 لعبة الحروف الأبجدية</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">ما هو الحرف الأول من كلمة "${question.arabic}"؟</p>
                    <p class="question-text-en">What is the first letter of "${question.word}"?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.word.toLowerCase()}_ar.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.word}', 'en', '${question.word.toLowerCase()}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn letter-option" onclick="checkAlphabetAnswer('${option.letter}', '${question.letter}')">
                            ${option.letter}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkAlphabetAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.letter-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.trim() === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.trim() === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

function loadColorsGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(colorsData).slice(0, 6);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(colorsData.filter(item => item.color !== question.color)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="colors-game">
                <h2>🎨 لعبة الألوان</h2>
                <div class="question-container">
                    <div class="color-display" style="background-color: ${question.hex}; width: 200px; height: 200px; border-radius: 20px; margin: 20px auto; border: 5px solid #333;"></div>
                    <p class="question-text">ما هو اسم هذا اللون؟</p>
                    <p class="question-text-en">What is the name of this color?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.color.toLowerCase()}_ar.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.color}', 'en', '${question.color.toLowerCase()}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn color-option" onclick="checkColorAnswer('${option.color}', '${question.color}')">
                            ${option.color}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkColorAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.color-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('ممتاز!', 'ar', 'excellent.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

function loadNumbersGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(numbersData).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(numbersData.filter(item => item.number !== question.number)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        // إنشاء عناصر بصرية للعدد
        const visualItems = Array(question.items).fill('⭐').join(' ');
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="numbers-game">
                <h2>🔢 لعبة الأرقام</h2>
                <div class="question-container">
                    <p class="question-text">كم عدد النجوم؟</p>
                    <p class="question-text-en">How many stars?</p>
                    <div class="visual-count" style="font-size: 2.5rem; margin: 20px auto; line-height: 1.5; text-align: center; max-width: 400px;">${visualItems}</div>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.number}.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.word}', 'en', '${question.number}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn number-option" onclick="checkNumberAnswer(${option.number}, ${question.number})">
                            ${option.number} - ${option.word}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkNumberAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.number-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.startsWith(correct.toString())) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.startsWith(selected.toString()) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('رائع!', 'ar', 'wonderful.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

// باقي الألعاب (العائلة، أجزاء الجسم، الحيوانات، الطعام) ستكون بنفس النمط
function loadFamilyGame() {
    loadGenericGame(familyData, 'العائلة', '👨‍👩‍👧‍👦', 'من هذا؟', 'Who is this?');
}

function loadBodyPartsGame() {
    loadGenericGame(bodyPartsData, 'أجزاء الجسم', '👤', 'ما هو هذا الجزء من الجسم؟', 'What is this body part?');
}

function loadAnimalsGame() {
    loadGenericGame(animalsData, 'الحيوانات', '🐾', 'ما هو هذا الحيوان؟', 'What is this animal?');
}

function loadFoodGame() {
    loadGenericGame(foodData, 'الطعام والشراب', '🍎', 'ما هذا؟', 'What is this?');
}

// بيانات لعبة الأمر البسيط
const commandData = [
    { command: 'Sit down', arabic: 'جلس', action: 'sit', emoji: '🪑' },
    { command: 'Stand up', arabic: 'نهض', action: 'stand', emoji: '🚶' },
    { command: 'Listen', arabic: 'استمع', action: 'listen', emoji: '👂' },
    { command: 'Look', arabic: 'انظر', action: 'look', emoji: '👀' },
    { command: 'Point', arabic: 'اشير', action: 'point', emoji: '👉' }
];

function loadCommandsGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(commandData).slice(0, 5);

    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }

        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(commandData.filter(item => item.command !== question.command)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="commands-game">
                <h2>👮 لعبة أوامر بسيطة</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <p class="question-text">ما هو الترجمة الصحيحة لهذا الأمر؟</p>
                    <p class="question-text-en">What is the correct translation for this command?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.command}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn command-option" onclick="checkCommandAnswer('${option.arabic}', '${question.arabic}')">
                            ${option.arabic}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkCommandAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.command-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
}

// بيانات لعبة التعريف بالنفس
const introductionData = [
    { name: 'Ahmed', age: 7, arabic: 'أحمد', english: 'I am Ahmed. I am 7 years old.' },
    { name: 'Fatima', age: 6, arabic: 'فاطمة', english: 'I am Fatima. I am 6 years old.' },
    { name: 'Khalid', age: 7, arabic: 'خالد', english: 'I am Khalid. I am 7 years old.' },
    { name: 'Sara', age: 6, arabic: 'سارة', english: 'I am Sara. I am 6 years old.' },
    { name: 'Youssef', age: 7, arabic: 'يوسف', english: 'I am Youssef. I am 7 years old.' }
];

function loadIntroductionGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(introductionData).slice(0, 5);

    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }

        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(introductionData.filter(item => item.name !== question.name)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="introduction-game">
                <h2>👤 لعبة التعريف بالنفس</h2>
                <div class="question-container">
                    <div class="avatar-display" style="font-size: 5rem; margin: 20px;">👦</div>
                    <p class="question-text">من هو هذا الطفل؟ ما عمره؟</p>
                    <p class="question-text-en">Who is this child? How old is he/she?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.english}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn intro-option" onclick="checkIntroductionAnswer('${option.name}', '${option.age}', '${question.name}', '${question.age}')">
                            ${option.name} - ${option.age} سنوات
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkIntroductionAnswer = function(selectedName, selectedAge, correctName, correctAge) {
        const isCorrect = selectedName === correctName && selectedAge === correctAge;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.intro-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correctName) && btn.textContent.includes(correctAge)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if ((btn.textContent.includes(selectedName) && btn.textContent.includes(selectedAge)) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
}

// وظيفة عامة للألعاب المتشابهة
function loadGenericGame(data, gameName, emoji, questionAr, questionEn) {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(data).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(data.filter(item => item.word !== question.word)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="generic-game">
                <h2>${emoji} لعبة ${gameName}</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji || '❓'}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">${questionAr}</p>
                    <p class="question-text-en">${questionEn}</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.word.toLowerCase()}_ar.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.word}', 'en', '${question.word.toLowerCase()}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn generic-option" onclick="checkGenericAnswer('${option.word}', '${question.word}')">
                            ${option.word}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkGenericAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.generic-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            enhancedSpeak('عظيم!', 'ar', 'great.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'try_again.mp3');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}
