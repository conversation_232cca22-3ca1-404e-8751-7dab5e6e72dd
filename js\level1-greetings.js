
// بيانات لعبة التحيات والآداب
const greetingsData = [
    { greeting: 'Hello', arabic: 'مرحباً', response: 'Hi!', audio: 'hello.mp3', emoji: '👋' },
    { greeting: 'Hi', arabic: 'أهلاً', response: 'Hello!', audio: 'hi.mp3', emoji: '👋' },
    { greeting: 'Goodbye', arabic: 'وداعاً', response: 'Bye!', audio: 'goodbye.mp3', emoji: '👋' },
    { greeting: 'Thank you', arabic: 'شكراً', response: 'You're welcome!', audio: 'thankyou.mp3', emoji: '🙏' },
    { greeting: 'Please', arabic: 'من فضلك', response: 'Sure!', audio: 'please.mp3', emoji: '🙏' },
    { greeting: 'Sorry', arabic: 'آسف', response: 'It's okay.', audio: 'sorry.mp3', emoji: '😔' }
];

// وظيفة لتحميل لعبة التحيات
function loadGreetingsGame() {
    try {
        console.log('Starting greetings game');
        let currentQuestionIndex = 0;
        const questions = shuffleArray(greetingsData).slice(0, 5);

        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                console.log('Greetings game completed, calling nextGame');
                nextGame();
                return;
            }

        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(greetingsData.filter(item => item.greeting !== question.greeting)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="greetings-game">
                <h2>🗣️ لعبة التحيات والآداب</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <p class="question-text">ما هو رد مناسب على كلمة "${question.arabic}"؟</p>
                    <p class="question-text-en">What is an appropriate response to "${question.greeting}"?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar', '${question.greeting.toLowerCase()}.mp3')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.greeting}', 'en', '${question.greeting.toLowerCase()}.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn greeting-option" onclick="checkGreetingAnswer('${option.response}', '${question.response}')">
                            ${option.response}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkGreetingAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.greeting-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
    } catch (error) {
        console.error('Error in loadGreetingsGame:', error);
        nextGame();
    }
}

// بيانات لعبة الجمل الاسمية البسيطة
const sentenceData = [
    { subject: 'This', object: 'book', arabic: 'هذه كتاب', image: 'images/book.png', correct: 'This is a book.' },
    { subject: 'That', object: 'cat', arabic: ' تلك قطة', image: 'images/cat.png', correct: 'That is a cat.' },
    { subject: 'This', object: 'pen', arabic: ' هذه قلم', image: 'images/pen.png', correct: 'This is a pen.' },
    { subject: 'That', object: 'dog', arabic: ' تلك كلب', image: 'images/dog.png', correct: 'That is a dog.' },
    { subject: 'This', object: 'apple', arabic: ' هذه تفاحة', image: 'images/apple.png', correct: 'This is an apple.' }
];

// وظيفة لتحميل لعبة الجمل الاسمية البسيطة
function loadSentencesGame() {
    try {
        console.log('Starting sentences game');
        let currentQuestionIndex = 0;
        const questions = shuffleArray(sentenceData).slice(0, 5);

        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                console.log('Sentences game completed, calling nextGame');
                nextGame();
                return;
            }

        const question = questions[currentQuestionIndex];
        const wrongAnswers = [
            question.subject + ' is not a ' + question.object + '.',
            question.subject + ' are a ' + question.object + '.',
            'Is ' + question.subject + ' a ' + question.object + '?'
        ];

        const allOptions = shuffleArray([question.correct, ...wrongAnswers]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="sentences-game">
                <h2>📝 لعبة الجمل الاسمية البسيطة</h2>
                <div class="question-container">
                    <img src="${question.image}" alt="${question.object}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">ما هي الجملة الصحيحة التي تصف هذا الشيء؟</p>
                    <p class="question-text-en">What is the correct sentence to describe this item?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.correct}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn sentence-option" onclick="checkSentenceAnswer('${option}', '${question.correct}')">
                            ${option}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkSentenceAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.sentence-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
    } catch (error) {
        console.error('Error in loadSentencesGame:', error);
        nextGame();
    }
}

// بيانات لعبة النفي البسيط
const negativeData = [
    { item: 'book', arabic: 'كتاب', correct: 'This is not a book.', wrong: 'This is a book.', image: 'images/book.png' },
    { item: 'pen', arabic: 'قلم', correct: 'This is not a pen.', wrong: 'This is a pen.', image: 'images/pen.png' },
    { item: 'apple', arabic: 'تفاحة', correct: 'This is not an apple.', wrong: 'This is an apple.', image: 'images/apple.png' },
    { item: 'cat', arabic: 'قطة', correct: 'That is not a cat.', wrong: 'That is a cat.', image: 'images/cat.png' },
    { item: 'dog', arabic: 'كلب', correct: 'That is not a dog.', wrong: 'That is a dog.', image: 'images/dog.png' }
];

// وظيفة لتحميل لعبة النفي البسيط
function loadNegativeGame() {
    try {
        console.log('Starting negative game');
        let currentQuestionIndex = 0;
        const questions = shuffleArray(negativeData).slice(0, 5);

        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                console.log('Negative game completed, calling nextGame');
                nextGame();
                return;
            }

        const question = questions[currentQuestionIndex];
        const allOptions = shuffleArray([question.correct, question.wrong]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="negative-game">
                <h2>❌ لعبة النفي البسيط</h2>
                <div class="question-container">
                    <img src="${question.image}" alt="${question.item}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">أكمل الجملة المناسبة:</p>
                    <p class="question-text-en">Complete the correct sentence:</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar')">🔊 العربية</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn negative-option" onclick="checkNegativeAnswer('${option}', '${question.correct}')">
                            ${option}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkNegativeAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.negative-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
    } catch (error) {
        console.error('Error in loadNegativeGame:', error);
        nextGame();
    }
}

// بيانات لعبة الأسئلة البسيطة
const questionData = [
    { item: 'book', arabic: 'كتاب', correct: 'Is this a book?', wrong: 'This is a book.', image: 'images/book.png' },
    { item: 'pen', arabic: 'قلم', correct: 'Is this a pen?', wrong: 'This is a pen.', image: 'images/pen.png' },
    { item: 'apple', arabic: 'تفاحة', correct: 'Is this an apple?', wrong: 'This is an apple.', image: 'images/apple.png' },
    { item: 'cat', arabic: 'قطة', correct: 'Is that a cat?', wrong: 'That is a cat.', image: 'images/cat.png' },
    { item: 'dog', arabic: 'كلب', correct: 'Is that a dog?', wrong: 'That is a dog.', image: 'images/dog.png' }
];

// وظيفة لتحميل لعبة الأسئلة البسيطة
function loadQuestionsGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(questionData).slice(0, 5);

    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }

        const question = questions[currentQuestionIndex];
        const allOptions = shuffleArray([question.correct, question.wrong]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="questions-game">
                <h2>❓ لعبة الأسئلة البسيطة</h2>
                <div class="question-container">
                    <img src="${question.image}" alt="${question.item}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">ما هي الصيغة الصحيحة للسؤال عن هذا الشيء؟</p>
                    <p class="question-text-en">What is the correct question form for this item?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.correct}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn question-option" onclick="checkQuestionAnswer('${option}', '${question.correct}')">
                            ${option}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkQuestionAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.question-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
}

// بيانات لعبة أداة التنكير a/an
const articleData = [
    { item: 'apple', image: 'images/apple.png', correct: 'an', wrong: 'a' },
    { item: 'book', image: 'images/book.png', correct: 'a', wrong: 'an' },
    { item: 'pen', image: 'images/pen.png', correct: 'a', wrong: 'an' },
    { item: 'elephant', image: 'images/elephant.png', correct: 'an', wrong: 'a' },
    { item: 'cat', image: 'images/cat.png', correct: 'a', wrong: 'an' }
];

// وظيفة لتحميل لعبة a/an
function loadArticlesGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(articleData).slice(0, 5);

    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }

        const question = questions[currentQuestionIndex];
        const allOptions = shuffleArray([question.correct, question.wrong]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="articles-game">
                <h2>📚 لعبة a/an</h2>
                <div class="question-container">
                    <img src="${question.image}" alt="${question.item}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">أكمل الجملة: "__ ${question.item}"</p>
                    <p class="question-text-en">Complete the sentence: "__ ${question.item}"</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.item}', 'en', '${question.item}_en.mp3')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn article-option" onclick="checkArticleAnswer('${option}', '${question.correct}')">
                            ${option}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkArticleAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.article-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
}

// بيانات لعبة الأوامر البسيطة
const commandsData = [
    { command: 'Sit down', arabic: 'جلس', action: 'sitting', emoji: '🪑' },
    { command: 'Stand up', arabic: 'قف', action: 'standing', emoji: '🚶' },
    { command: 'Listen', arabic: 'استمع', action: 'listening', emoji: '👂' },
    { command: 'Look', arabic: 'انظر', action: 'looking', emoji: '👀' },
    { command: 'Point', arabic: 'اشير', action: 'pointing', emoji: '👉' }
];

// وظيفة لتحميل لعبة الأوامر البسيطة
function loadCommandsGame() {
    try {
        console.log('Starting commands game');
        let currentQuestionIndex = 0;
        const questions = shuffleArray(commandsData).slice(0, 5);

        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                console.log('Commands game completed, calling nextGame');
                nextGame();
                return;
            }

        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(commandsData.filter(item => item.command !== question.command)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);

        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="commands-game">
                <h2>👂 لعبة الأوامر البسيطة</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <p class="question-text">ما هذا الأمر؟ "${question.arabic}"</p>
                    <p class="question-text-en">What is this command? "${question.command}"</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="enhancedSpeak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="enhancedSpeak('${question.command}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn command-option" onclick="checkCommandAnswer('${option.command}', '${question.command}')">
                            ${option.command}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }

    window.checkCommandAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);

        // تلوين الأزرار
        document.querySelectorAll('.command-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });

        if (isCorrect) {
            enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
            celebrateCorrectAnswer();
        } else {
            enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
        }

        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };

    showQuestion();
    } catch (error) {
        console.error('Error in loadCommandsGame:', error);
        nextGame();
    }
}





// بيانات لعبة العائلة الموسعة (لإضافة mummy, daddy, baby)
const extendedFamilyData = [
    { word: 'Father', arabic: 'أب', image: 'images/father.png', description: 'Dad', emoji: '👨' },
    { word: 'Mother', arabic: 'أم', image: 'images/mother.png', description: 'Mom', emoji: '👩' },
    { word: 'Brother', arabic: 'أخ', image: 'images/brother.png', description: 'Boy sibling', emoji: '👦' },
    { word: 'Sister', arabic: 'أخت', image: 'images/sister.png', description: 'Girl sibling', emoji: '👧' },
    { word: 'Grandfather', arabic: 'جد', image: 'images/grandfather.png', description: 'Grandpa', emoji: '👴' },
    { word: 'Grandmother', arabic: 'جدة', image: 'images/grandmother.png', description: 'Grandma', emoji: '👵' },
    { word: 'Mummy', arabic: 'ماما', image: 'images/mummy.png', description: 'Mom', emoji: '👩‍👧' },
    { word: 'Daddy', arabic: 'بابا', image: 'images/daddy.png', description: 'Dad', emoji: '👨‍👦' },
    { word: 'Baby', arabic: 'رضيع', image: 'images/baby.png', description: 'Infant', emoji: '👶' },
    { word: 'Boy', arabic: 'ولد', image: 'images/boy.png', description: 'Male child', emoji: '👦' },
    { word: 'Girl', arabic: 'بنت', image: 'images/girl.png', description: 'Female child', emoji: '👧' },
    { word: 'Teacher', arabic: 'معلم', image: 'images/teacher.png', description: 'School teacher', emoji: '👩‍🏫' },
    { word: 'Friend', arabic: 'صديق', image: 'images/friend.png', description: 'Companion', emoji: '🤝' }
];

// وظيفة لتحميل لعبة العائلة الموسعة
function loadExtendedFamilyGame() {
    loadGenericGame(extendedFamilyData, 'العائلة الموسعة', '👨‍👩‍👧‍👦', 'من هذا؟', 'Who is this?');
}

// بيانات لعبة الأشياء في الصف
const classroomData = [
    { word: 'book', arabic: 'كتاب', image: 'images/book.png', emoji: '📚' },
    { word: 'pen', arabic: 'قلم', image: 'images/pen.png', emoji: '🖊️' },
    { word: 'pencil', arabic: 'قلم رصاص', image: 'images/pencil.png', emoji: '✏️' },
    { word: 'bag', arabic: 'حقيبة', image: 'images/bag.png', emoji: '🎒' },
    { word: 'desk', arabic: 'مكتب', image: 'images/desk.png', emoji: '🪑' },
    { word: 'chair', arabic: 'كرسي', image: 'images/chair.png', emoji: '💺' },
    { word: 'eraser', arabic: 'ممحاة', image: 'images/eraser.png', emoji: '🧹' },
    { word: 'ruler', arabic: 'مسطرة', image: 'images/ruler.png', emoji: '📏' }
];

// وظيفة لتحميل لعبة الأشياء في الصف
function loadClassroomGame() {
    loadGenericGame(classroomData, 'الأشياء في الصف', '📚', 'ما هذا؟', 'What is this?');
}
