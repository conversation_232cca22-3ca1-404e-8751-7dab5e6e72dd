
// بيانات لعبة التلوين
const coloringItems = [
    { name: 'apple', arabic: 'تفاحة', color: '#FF0000', image: 'images/apple.png' },
    { name: 'banana', arabic: 'موزة', color: '#FFFF00', image: 'images/banana.png' },
    { name: 'sky', arabic: 'سماء', color: '#0000FF', image: 'images/sky.png' },
    { name: 'grass', arabic: 'عشب', color: '#00FF00', image: 'images/grass.png' },
    { name: 'sun', arabic: 'شمس', color: '#FFA500', image: 'images/sun.png' }
];

// وظيفة لتحميل لعبة التلوين
function loadColoringGame() {
    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div class="coloring-game">
            <h2>🎨 لعبة التلوين</h2>
            <p class="instructions">اختر اللون الصحيح لتلوين الكائن</p>
            <p class="instructions-en">Choose the correct color to paint the object</p>

            <div class="coloring-container">
                <div class="coloring-board">
                    <canvas id="coloringCanvas" width="400" height="400"></canvas>
                    <div class="color-palette">
                        ${generateColorPalette()}
                    </div>
                </div>

                <div class="coloring-items">
                    ${generateColoringItems()}
                </div>
            </div>

            <div class="coloring-controls">
                <button class="coloring-btn" onclick="clearCanvas()">مسح الرسم</button>
                <button class="coloring-btn" onclick="checkColoring()">التحقق من الإجابة</button>
                <button class="coloring-btn" onclick="nextGame()">التالي</button>
            </div>
        </div>
    `;

    // تهيئة اللوحة والرسم
    initializeColoringGame();
}

// توليد لوحة الألوان
function generateColorPalette() {
    const colors = [
        { hex: '#FF0000', name: 'red', arabic: 'أحمر' },
        { hex: '#0000FF', name: 'blue', arabic: 'أزرق' },
        { hex: '#FFFF00', name: 'yellow', arabic: 'أصفر' },
        { hex: '#00FF00', name: 'green', arabic: 'أخضر' },
        { hex: '#FFA500', name: 'orange', arabic: 'برتقالي' },
        { hex: '#800080', name: 'purple', arabic: 'بنفسجي' },
        { hex: '#000000', name: 'black', arabic: 'أسود' },
        { hex: '#FFFFFF', name: 'white', arabic: 'أبيض' }
    ];

    return colors.map(color => `
        <div class="color-item" 
             style="background-color: ${color.hex}; ${color.hex === '#FFFFFF' ? 'border: 1px solid #ccc;' : ''}"
             data-color="${color.hex}"
             data-name="${color.name}"
             data-arabic="${color.arabic}"
             onclick="selectColor('${color.hex}')">
            <span class="color-name">${color.arabic}</span>
        </div>
    `).join('');
}

// توليد عناصر التلوين
function generateColoringItems() {
    return coloringItems.map(item => `
        <div class="coloring-item" 
             data-name="${item.name}"
             data-arabic="${item.arabic}"
             data-color="${item.color}"
             onclick="selectItemForColoring('${item.name}')">
            <img src="${item.image}" alt="${item.name}" class="coloring-item-image">
            <span class="coloring-item-name">${item.arabic}</span>
        </div>
    `).join('');
}

// متغيرات اللعبة
let canvas, ctx;
let selectedColor = null;
let selectedItems = [];
let currentCorrectColor = null;
let currentCorrectItem = null;

// تهيئة اللعبة
function initializeColoringGame() {
    canvas = document.getElementById('coloringCanvas');
    ctx = canvas.getContext('2d');

    // ضبط حجم اللوحة
    const container = canvas.parentElement;
    const maxWidth = container.clientWidth - 40;
    canvas.width = Math.min(400, maxWidth);
    canvas.height = canvas.width;

    // رسم خلفية بيضاء
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // اختيار عنصر عشوائي للتلوين
    selectRandomItem();
}

// اختيار عنصر عشوائي
function selectRandomItem() {
    const randomIndex = Math.floor(Math.random() * coloringItems.length);
    currentCorrectItem = coloringItems[randomIndex];
    currentCorrectColor = currentCorrectItem.color;

    // عرض رسالة للمستخدم
    const message = document.createElement('div');
    message.className = 'coloring-message';
    message.innerHTML = `
        <p class="question-text">لون ${currentCorrectItem.arabic} هو؟</p>
        <p class="question-text-en">What is the color of ${currentCorrectItem.name}?</p>
        <div class="speak-container">
            <button class="speak-btn" onclick="enhancedSpeak('${currentCorrectItem.arabic}', 'ar')">🔊 العربية</button>
            <button class="speak-btn" onclick="enhancedSpeak('${currentCorrectItem.name}', 'en')">🔊 English</button>
        </div>
    `;

    const gameContent = document.querySelector('.coloring-game');
    const messageContainer = gameContent.querySelector('.coloring-message');
    if (messageContainer) {
        messageContainer.remove();
    }
    gameContent.insertBefore(message, gameContent.querySelector('.coloring-container'));
}

// اختيار لون
function selectColor(color) {
    selectedColor = color;

    // تحديث واجهة المستخدم
    document.querySelectorAll('.color-item').forEach(item => {
        if (item.dataset.color === color) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
}

// اختيار عنصر للتلوين
function selectItemForColoring(itemName) {
    if (!selectedColor) {
        enhancedSpeak('يرجى اختيار لون أولاً', 'ar');
        return;
    }

    const item = coloringItems.find(i => i.name === itemName);
    if (!item) return;

    // رسم العنصر باللون المختار
    drawItem(item, selectedColor);

    // إضافة العنصر إلى قائمة العناصر المطلوبة تلوينها
    if (!selectedItems.find(i => i.name === itemName)) {
        selectedItems.push({
            name: itemName,
            color: selectedColor,
            correctColor: item.color
        });
    }
}

// رسم عنصر على اللوحة
function drawItem(item, color) {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const size = Math.min(canvas.width, canvas.height) * 0.3;

    // حفظ حالة اللوحة
    ctx.save();

    // تعيين اللون
    ctx.fillStyle = color;

    // رسم دائلة أساسية (يمكن تحسين هذا ليشبه العنصر الفعلي)
    ctx.beginPath();
    ctx.arc(centerX, centerY, size, 0, Math.PI * 2);
    ctx.fill();

    // إضافة اسم العنصر
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(item.arabic, centerX, centerY);

    // استعادة حالة اللوحة
    ctx.restore();
}

// مسح اللوحة
function clearCanvas() {
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    selectedItems = [];
}

// التحقق من الإجابة
function checkColoring() {
    if (!selectedItems.length) {
        enhancedSpeak('يرجى تلوين عنصر أولاً', 'ar');
        return;
    }

    let correctCount = 0;
    let totalItems = selectedItems.length;

    selectedItems.forEach(item => {
        if (item.color === item.correctColor) {
            correctCount++;
        }
    });

    // حساب النتيجة
    const percentage = Math.round((correctCount / totalItems) * 100);
    recordAnswer(percentage >= 70);

    // عرض النتائج
    const gameContent = document.querySelector('.coloring-game');
    const resultMessage = document.createElement('div');
    resultMessage.className = 'coloring-result';

    if (percentage >= 70) {
        resultMessage.innerHTML = `
            <div class="result-success">
                <p>أحسنت! لقد اخترت الألوان الصحيحة بنسبة ${percentage}%</p>
                <p>Great! You chose the correct colors ${percentage}% of the time</p>
            </div>
        `;
        enhancedSpeak('أحسنت!', 'ar', 'correct.mp3');
        celebrateCorrectAnswer();
    } else {
        resultMessage.innerHTML = `
            <div class="result-error">
                <p>حاول مرة أخرى! لقد اخترت الألوان الصحيحة بنسبة ${percentage}%</p>
                <p>Try again! You chose the correct colors ${percentage}% of the time</p>
            </div>
        `;
        enhancedSpeak('حاول مرة أخرى', 'ar', 'wrong.mp3');
    }

    // إضافة النتيجة إلى اللعبة
    const existingResult = gameContent.querySelector('.coloring-result');
    if (existingResult) {
        existingResult.remove();
    }
    gameContent.appendChild(resultMessage);

    // الانتقال للسؤال التالي بعد فترة
    setTimeout(() => {
        nextGame();
    }, 3000);
}

// إضافة أنماط CSS لعبة التلوين
const coloringStyles = document.createElement('style');
coloringStyles.textContent = `
    .coloring-game {
        text-align: center;
        padding: 20px;
    }

    .coloring-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .coloring-board {
        flex: 1;
        min-width: 300px;
        margin: 0 10px;
    }

    .coloring-items {
        flex: 1;
        min-width: 300px;
        margin: 0 10px;
    }

    #coloringCanvas {
        border: 2px solid #ddd;
        border-radius: 10px;
        background: white;
        cursor: crosshair;
        display: block;
        margin: 0 auto;
    }

    .color-palette {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        margin-top: 15px;
    }

    .color-item {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        cursor: pointer;
        position: relative;
        transition: transform 0.2s;
        border: 3px solid transparent;
    }

    .color-item:hover {
        transform: scale(1.1);
    }

    .color-item.selected {
        border: 3px solid #333;
        transform: scale(1.15);
    }

    .color-name {
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        white-space: nowrap;
    }

    .coloring-item {
        text-align: center;
        margin: 15px;
        cursor: pointer;
        padding: 10px;
        border-radius: 10px;
        transition: all 0.3s;
    }

    .coloring-item:hover {
        background: rgba(76, 175, 80, 0.1);
        transform: translateY(-5px);
    }

    .coloring-item-image {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-bottom: 5px;
    }

    .coloring-item-name {
        font-size: 1rem;
        color: #333;
        font-weight: bold;
    }

    .coloring-controls {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .coloring-btn {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s;
    }

    .coloring-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .coloring-message {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border: 2px solid #e9ecef;
    }

    .coloring-result {
        margin-top: 20px;
        padding: 15px;
        border-radius: 10px;
    }

    .result-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .result-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
        .coloring-container {
            flex-direction: column;
        }

        .coloring-board, .coloring-items {
            width: 100%;
            margin: 10px 0;
        }

        #coloringCanvas {
            max-width: 100%;
            height: auto;
        }
    }
`;
document.head.appendChild(coloringStyles);
