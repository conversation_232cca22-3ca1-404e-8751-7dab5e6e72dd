// متغيرات اللعبة الرئيسية
let currentLevel = 1;
let currentGameIndex = 0;
let totalScore = 0;
let currentScore = 0;
let correctAnswers = 0;
let totalQuestions = 0;
let gameData = [];

// إعدادات اللعبة
const gameSettings = {
    soundEnabled: true,
    animationsEnabled: true,
    difficulty: 'normal'
};

// بيانات المستويات
const levelData = {
    1: {
        name: "المستوى الأول - الصف الأول الابتدائي",
        games: ["alphabet", "colors", "numbers", "family", "body", "animals", "food", "greetings", "sentences", "negative", "coloring", "matching", "commands", "introduction"],
        totalGames: 14
    },
    2: {
        name: "المستوى الثاني - الصف الثاني الابتدائي", 
        games: ["school", "days", "clothes", "farm-animals", "nature"],
        totalGames: 5
    },
    3: {
        name: "المستوى الثالث - الصف الثالث الابتدائي",
        games: ["feelings", "health", "city", "jobs", "environment"],
        totalGames: 5
    },
    4: {
        name: "المستوى الرابع - الصف الرابع الابتدائي",
        games: ["advanced-food", "habitats", "plants", "buildings", "devices"],
        totalGames: 5
    },
    5: {
        name: "المستوى الخامس - الصف الخامس الابتدائي",
        games: ["farming", "sports", "climate", "tourism", "wildlife"],
        totalGames: 5
    },
    6: {
        name: "المستوى السادس - الصف السادس الابتدائي",
        games: ["community", "climate-change", "civilizations", "technology", "stories"],
        totalGames: 5
    }
};

// وظائف التنقل بين الشاشات
function showScreen(screenId) {
    // إخفاء جميع الشاشات
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    
    // إظهار الشاشة المطلوبة
    document.getElementById(screenId).classList.add('active');
}

function showWelcomeScreen() {
    showScreen('welcome-screen');
    resetGame();
}

function showLevelSelection() {
    showScreen('level-selection');
}

function showGameScreen() {
    showScreen('game-screen');
    updateGameHeader();
}

function showResultsScreen() {
    showScreen('results-screen');
    displayResults();
    // إضافة احتفال عند عرض النتائج
    setTimeout(() => {
        celebrateGameCompletion();
    }, 500);
}

// وظائف اللعبة الرئيسية
function startLevel(level) {
    currentLevel = level;
    currentGameIndex = 0;
    totalScore = 0;
    correctAnswers = 0;
    totalQuestions = 0;
    
    showGameScreen();
    loadCurrentGame();
}

function loadCurrentGame() {
    const level = levelData[currentLevel];
    if (currentGameIndex >= level.games.length) {
        // انتهاء المستوى
        showResultsScreen();
        return;
    }
    
    const gameName = level.games[currentGameIndex];
    updateGameHeader();
    
    // تحميل اللعبة المناسبة
    switch(gameName) {
        case 'alphabet':
            loadAlphabetGame();
            break;
        case 'colors':
            loadColorsGame();
            break;
        case 'numbers':
            loadNumbersGame();
            break;
        case 'family':
            loadFamilyGame();
            break;
        case 'body':
            loadBodyPartsGame();
            break;
        case 'animals':
            loadAnimalsGame();
            break;
        case 'food':
            loadFoodGame();
            break;
        case 'greetings':
            loadGreetingsGame();
            break;
        case 'sentences':
            loadSentencesGame();
            break;
        case 'negative':
            loadNegativeGame();
            break;
        case 'coloring':
            loadColoringGame();
            break;
        case 'matching':
            loadMatchingGame();
            break;
        case 'commands':
            loadCommandsGame();
            break;
        case 'introduction':
            loadIntroductionGame();
            break;
        default:
            loadPlaceholderGame(gameName);
    }
}

function updateGameHeader() {
    const level = levelData[currentLevel];
    document.getElementById('current-level').textContent = level.name;
    
    const gameNames = {
        'alphabet': 'لعبة الحروف الأبجدية',
        'colors': 'لعبة الألوان',
        'numbers': 'لعبة الأرقام',
        'family': 'لعبة أفراد العائلة',
        'body': 'لعبة أجزاء الجسم',
        'animals': 'لعبة الحيوانات',
        'food': 'لعبة الطعام والشراب',
        'greetings': 'لعبة التحيات والآداب',
        'sentences': 'لعبة الجمل الاسمية',
        'negative': 'لعبة النفي البسيط',
        'coloring': 'لعبة التلوين',
        'matching': 'لعبة توصيل الكلمة بالصورة',
        'commands': 'لعبة أوامر بسيطة',
        'introduction': 'لعبة التعريف بالنفس'
    };
    
    const currentGameName = level.games[currentGameIndex];
    document.getElementById('current-game').textContent = gameNames[currentGameName] || currentGameName;
    
    // تحديث شريط التقدم
    const progress = ((currentGameIndex + 1) / level.totalGames) * 100;
    document.getElementById('progress-bar').style.width = progress + '%';
    
    // تحديث النقاط
    document.getElementById('score-display').textContent = `النقاط: ${totalScore}`;
}

function nextGame() {
    currentGameIndex++;
    loadCurrentGame();
}

function restartCurrentLevel() {
    startLevel(currentLevel);
}

function goToNextLevel() {
    if (currentLevel < 6) {
        startLevel(currentLevel + 1);
    } else {
        showLevelSelection();
    }
}

function resetGame() {
    currentLevel = 1;
    currentGameIndex = 0;
    totalScore = 0;
    currentScore = 0;
    correctAnswers = 0;
    totalQuestions = 0;
    gameData = [];
}

// وظائف النتائج والتقييم
function displayResults() {
    const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    
    // تحديث عنوان النتائج
    let title = "أحسنت!";
    let stars = 1;
    let encouragement = "استمر في التعلم!";
    
    if (percentage >= 90) {
        title = "ممتاز! 🌟";
        stars = 3;
        encouragement = "أنت نجم حقيقي! استمر في التألق!";
    } else if (percentage >= 70) {
        title = "جيد جداً! ⭐";
        stars = 2;
        encouragement = "أداء رائع! يمكنك تحسين أكثر!";
    } else if (percentage >= 50) {
        title = "جيد! 👍";
        stars = 1;
        encouragement = "بداية جيدة! استمر في الممارسة!";
    } else {
        title = "حاول مرة أخرى! 💪";
        stars = 1;
        encouragement = "لا تستسلم! التعلم يحتاج إلى صبر!";
    }
    
    document.getElementById('results-title').textContent = title;
    document.getElementById('final-score').textContent = totalScore;
    document.getElementById('correct-answers').textContent = correctAnswers;
    document.getElementById('total-questions').textContent = totalQuestions;
    document.getElementById('encouragement-text').textContent = encouragement;
    
    // إظهار النجوم
    const starsContainer = document.querySelector('.results-stars');
    starsContainer.innerHTML = '';
    for (let i = 0; i < 3; i++) {
        const star = document.createElement('span');
        star.className = 'star';
        star.textContent = i < stars ? '⭐' : '☆';
        starsContainer.appendChild(star);
    }
    
    // إظهار زر المستوى التالي إذا كان الأداء جيد
    const nextLevelBtn = document.querySelector('.next-level-btn');
    if (percentage >= 60 && currentLevel < 6) {
        nextLevelBtn.style.display = 'inline-block';
    } else {
        nextLevelBtn.style.display = 'none';
    }
}

// وظائف مساعدة
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

function playSound(soundName) {
    if (!gameSettings.soundEnabled) return;
    
    // يمكن إضافة تشغيل الأصوات هنا
    console.log(`Playing sound: ${soundName}`);
}

function speak(text, lang = 'ar') {
    if ('speechSynthesis' in window && gameSettings.soundEnabled) {
        speechSynthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
        utterance.rate = 0.7;
        utterance.pitch = 1.1;
        
        speechSynthesis.speak(utterance);
    }
}

function addScore(points) {
    currentScore += points;
    totalScore += points;
    document.getElementById('score-display').textContent = `النقاط: ${totalScore}`;
}

function recordAnswer(isCorrect) {
    totalQuestions++;
    if (isCorrect) {
        correctAnswers++;
        addScore(10);
    }
}

// وظيفة لتحميل لعبة مؤقتة للألعاب غير المطورة بعد
function loadPlaceholderGame(gameName) {
    const gameContent = document.getElementById('game-content');
    gameContent.innerHTML = `
        <div style="text-align: center; padding: 50px;">
            <h2>🚧 قريباً 🚧</h2>
            <p>لعبة ${gameName} قيد التطوير</p>
            <button onclick="nextGame()" style="
                background: #4CAF50;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 25px;
                font-size: 1.2rem;
                cursor: pointer;
                margin-top: 20px;
            ">التالي</button>
        </div>
    `;
}

// تهيئة اللعبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🍍 جزيرة الأناناس - تم تحميل اللعبة بنجاح!');
    showWelcomeScreen();
});
